package org.changneng.framework.frameworkbusiness.service.impl;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.dao.*;
import org.changneng.framework.frameworkbusiness.dao.refinetemplate.SceneCheckEntryMapper;
import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneCheckEntry;
import org.changneng.framework.frameworkbusiness.entity.vo.CheckItemConfigTreeVO;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneSysModelResult;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneSysModelSeach;
import org.changneng.framework.frameworkbusiness.pdf.GeneratePdfService;
import org.changneng.framework.frameworkbusiness.service.CheckItemConfigService;
import org.changneng.framework.frameworkbusiness.service.JcblService;
import org.changneng.framework.frameworkbusiness.service.LocalExamineService;
import org.changneng.framework.frameworkbusiness.service.SystemCodingService;
//--↓↓↓↓↓↓↓↓↓↓---
import org.changneng.framework.frameworkbusiness.entity.dto.EnvSupervisionItemDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
//----------↑↑↑↑↑↑-----
import org.changneng.framework.frameworkbusiness.utils.CompareObjModified;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageHelper;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Service
@Transactional
public class LocalExamineServiceImpl implements LocalExamineService {

	private static Logger logger = LogManager.getLogger(TaskFlowServiceImpl.class.getName());
	@Autowired
	private LocalCheckMapper localCheckMapper;

	@Autowired
	private SystemCodingService systemCodingService;

	@Autowired
	private SceneCheckEntryMapper sceneCheckEntryMapper;

	@Autowired
	private SysUsersMapper sysUsersMapper;
	@Autowired
	private LocalCheckItemMapper localCheckItemMapper;

	@Autowired
	private SurveyRecordMapper surveyRecordMapper;

	@Autowired
	private TaskMapper taskMapper;

	@Autowired
	private LawEnforceObjectMapper lawEnforceObjectMapper;

	@Autowired
	private JcblService jcblService;
	@Autowired
	private  GeneratePdfService  generatePdfService;

	@Autowired
	private ScanningAttachmentMapper attachmentMapper;

	@Autowired
	private LawobjUpdateLogMapper lawobjUpdateLogMapper;

	@Autowired
	private SysDepartmentMapper sysDepartmentMapper;

	@Autowired
	private SceneDefaultModelMapper sceneDefaultModelMapper;

	@Autowired
	private SceneItemDatabaseMapper sceneItemDatabaseMapper;

	@Autowired
	private SceneSysModelMapper SceneSysModelMapper;

	@Autowired
	private SceneCustomModelMapper sceneCustomModelMapper;

	@Autowired
	private SceneCustomItemMapper sceneCustomItemMapper;

	@Autowired
	private SceneUsuallyModelMapper sceneUsuallyModelMapper;

	@Autowired
	private historyLawEnforceObjectMapper historyLawEnforceObjectMapper;

	@Autowired
	private SceneSysModelMapper sceneSysModelMapper;

	@Autowired
	private SysFilesMapper sysFilesMapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseJson saveLocalExamine(LocalCheck localCheck,String taskId ,String chickItemList,SysUsers sysUser, List<SysFiles> filesList)throws Exception {

		// 处理文件关联逻辑
		if (filesList != null && !filesList.isEmpty()) {
			StringBuilder fileIdBuilder = new StringBuilder();
			StringBuilder fileNameBuilder = new StringBuilder();
			for (SysFiles file : filesList) {
				fileIdBuilder.append(file.getId()).append(",");
				fileNameBuilder.append(file.getFileName()).append(","); // 获取文件名
			}
			String fileIdStr = fileIdBuilder.toString();
			if (fileIdStr.endsWith(",")) {
				fileIdStr = fileIdStr.substring(0, fileIdStr.length() - 1);
			}
			localCheck.setAdministrativeNoticeAttachment(fileIdStr); // 存储多个文件 ID，逗号分隔

			String fileNameStr = fileNameBuilder.toString();
			if (fileNameStr.endsWith(",")) {
				fileNameStr = fileNameStr.substring(0, fileNameStr.length() - 1);
			}
			localCheck.setAttachmentFileName(fileNameStr); // 存储多个文件名，逗号分隔


		}


		//维护污染源数据
		//SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		LawEnforceObjectWithBLOBs oldBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(localCheck.getLawObjectId());
		LawEnforceObjectWithBLOBs newBLOBs = new LawEnforceObjectWithBLOBs();
		LocalCheckPDFBean bean = new LocalCheckPDFBean();

		String isIllegalactCode = localCheck.getIsIllegalactCode();
		FastDFSClient fd=new FastDFSClient("classpath:fdfs_client.conf");
		String checkEndDateStr = null;
		String checkStartDateStr = null ;
		try {
		if(oldBLOBs != null ){
			if("1".equals(oldBLOBs.getTypeCode()) ){
				//企业类型修改污染源日志
				BeanUtils.copyProperties(oldBLOBs, newBLOBs);
				String legalPerson = localCheck.getLegalPerson();
				String legalPhone = localCheck.getLegalPhone();
				/*String legalManIdCard = localCheck.getLegalManIdCard();
				String chargeManIdCard = localCheck.getChargeManIdCard();*/
				if("1".equals(localCheck.getUpdateObjectState())){
					//同步当事人信息
					if(!ChangnengUtil.isNull(legalPerson) && !ChangnengUtil.isNull(legalPhone)){
						lawEnforceObjectMapper.updateLocalExamine(localCheck.getLawObjectId(),legalPerson,legalPhone);
						//污染源日志的记录
						newBLOBs.setLegalPerson(legalPerson);
						newBLOBs.setLegalPhone(legalPhone);
						//newBLOBs.setLegalManIdCard(legalManIdCard);
						//newBLOBs.setChargeManIdCard(chargeManIdCard);
						SysDepartment sysDepartment = sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId());
						sysUser.setBelongDepartmentName(sysDepartment.getDepartmentName());
						List<LawobjUpdateLog> contrastLawObjectModified = CompareObjModified.contrastLawObjectModified(oldBLOBs, newBLOBs, sysUser);
						if(contrastLawObjectModified.size()>0){
							lawobjUpdateLogMapper.insertSelectiveList(contrastLawObjectModified);
						}
					}
				}
				historyLawEnforceObjectWithBLOBs lawEnforceObject = new historyLawEnforceObjectWithBLOBs();
				lawEnforceObject.setTaskId(taskId);
				lawEnforceObject.setLawObjectId(localCheck.getLawObjectId());
				if(!ChangnengUtil.isNull(localCheck.getLegalPerson())&& !ChangnengUtil.isNull(localCheck.getLegalPhone())){
					//维护到快照表
					lawEnforceObject.setLegalPerson(localCheck.getLegalPerson());
					lawEnforceObject.setLegalPhone(localCheck.getLegalPhone());
					//lawEnforceObject.setLegalManIdCard(legalManIdCard);
					//lawEnforceObject.setChargeManIdCard(chargeManIdCard);
					lawEnforceObject.setAddress(localCheck.getAddress());
					lawEnforceObject.setUpdatetime(new Date());
					historyLawEnforceObjectMapper.updateBytaskIdAndObjectId(lawEnforceObject);
				}else{
					lawEnforceObject.setLegalPerson("");
					lawEnforceObject.setLegalPhone("");
					lawEnforceObject.setAddress(localCheck.getAddress());
					lawEnforceObject.setUpdatetime(new Date());
					historyLawEnforceObjectMapper.updateBytaskIdAndObjectId(lawEnforceObject);
				}
			}
		}
		//修改污染源信息
		Date checkStartDate = localCheck.getCheckStartDate();
		Date checkEndDate = localCheck.getCheckEndDate();
		if(checkEndDate != null){
			 checkEndDateStr =DateUtil.getSimpleFormate(checkEndDate);
		}
		if(checkStartDate != null){
			 checkStartDateStr = DateUtil.getSimpleFormate(checkStartDate);
		}
		if(localCheck.getcheckUserIds()!= null && !"".equals(localCheck.getcheckUserIds())){
			String[] arr  = localCheck.getcheckUserIds().split(",");
			bean.setCheckUserLength(arr.length);
		}
		//格式化输出时间
		if(!"".equals(checkStartDateStr) && checkStartDateStr != null){
			bean.setStartYear(checkStartDateStr.substring(0, 4));
			bean.setStartMouth(checkStartDateStr.substring(5, 7));
			bean.setStartDay(checkStartDateStr.substring(8, 11));
			bean.setStartHour(checkStartDateStr.substring(11, 13));
			bean.setStartMinute(checkStartDateStr.substring(14));
		}
		if(!"".equals(checkEndDateStr) && checkEndDateStr != null){
			bean.setEndYear(checkEndDateStr.substring(0, 4));
			bean.setEndMouth(checkEndDateStr.substring(5, 7));
			bean.setEndDay(checkEndDateStr.substring(8, 11));
			bean.setEndHour(checkEndDateStr.substring(11, 13));
			bean.setEndMinute(checkEndDateStr.substring(14));
		}
			//获取部门信息
			String queryHBJNameByAreacode = surveyRecordMapper.queryHBJNameByAreacode(sysUser.getBelongAreaId());
			bean.setName(queryHBJNameByAreacode);
			String makeUnitName = localCheck.getMakeUnitName();
			if(ChangnengUtil.isNull(makeUnitName)){
				bean.setName(queryHBJNameByAreacode);
			}else{
				bean.setName(makeUnitName);
			}

			bean.setAddress(localCheck.getAddress());
			bean.setObjectName(localCheck.getObjectName());
			bean.setLegalPerson(localCheck.getLegalPerson());
			bean.setcheckUserNames(localCheck.getcheckUserNames());;
			bean.setLawEnforcIds(localCheck.getLawEnforcIds());;
			bean.setLocalPerson(localCheck.getLocalPerson());
			bean.setLegalPhone(localCheck.getLegalPhone());
			bean.setLocalPersonJob(localCheck.getLocalPersonJob());
			bean.setInformDeptName(localCheck.getInformDeptName());
			bean.setInformLawIds(localCheck.getInformLawIds());
			bean.setRecordUserId(localCheck.getRecordUserId());
			bean.setRecordUserName(localCheck.getRecordUserName());
			bean.setParticipant(localCheck.getParticipant());//参与人员
         	//由于html中纯数字和英文版的逗号 不会自动换行 所以将英文逗号转换为中文逗号 处理自动换行问题。
         	//执法证号（告知事项部分）
         	if(bean.getInformLawIds()!=null && !bean.getInformLawIds().equals("")) {
         		String temp = bean.getInformLawIds();
         		bean.setInformLawIds(temp.replaceAll(",", "，"));
         	}
         	//执法证号
         	if(bean.getLawEnforcIds()!=null && !bean.getLawEnforcIds().equals("")) {
         		String temp = bean.getLawEnforcIds();
         		bean.setLawEnforcIds(temp.replaceAll(",", "，"));
         	}
			if(!ChangnengUtil.isNull(localCheck.getCheckSummary())){
				bean.setCheckSummary(localCheck.getCheckSummary().replace("\n","<br/>"));
			}
			bean.setLocalPersonPhone(localCheck.getLocalPersonPhone());
			//bean.setLegalManIdCard(localCheck.getLegalManIdCard());
			//bean.setChargeManIdCard(localCheck.getChargeManIdCard());
			localCheck.setLastUpdateDate(new Date());
			Task task = jcblService.taskByTaskId(taskId);
			//维护是否存在违法行为名称:是，否，疑似 任务task表  V2.0.3 放弃维护这个字段。
			if(!ChangnengUtil.isNull(taskId)){
				Task taskBean = new Task();
				taskBean.setId(taskId);
				if(!ChangnengUtil.isNull(localCheck.getUpdateObjectState())){
					taskBean.setSynchronizationStatus(Integer.parseInt(localCheck.getUpdateObjectState()));
				}
				//taskBean.setIsIllegalactName(localCheck.getIsIllegalactCode()); V2.0.3 放弃维护是否存在违法行为名称。
				taskMapper.updateByPrimaryKeySelective(taskBean);
			}
			if(!ChangnengUtil.isNull(localCheck.getId())){
						List<LocalCheckItem> chickItemItem  = null;
						//修改操作1
			            String docUrl = localCheck.getDocUrl();
			            LocalCheck localCheckTemp = localCheckMapper.selectByPrimaryKey(localCheck.getId());
			            if(localCheckTemp.getIsAppHandle() != localCheck.getIsAppHandle()){
			            	localCheck.setIsAppHandle(2);//web和app同时操作过；
			            }
			        	localCheck.setSaveStatus("1");
			        	localCheck.setUpdateUserName(sysUser.getLoginname());
			        	localCheck.setUpdateUserId(sysUser.getId());
			        	//行距倍数
			    		if(!ChangnengUtil.isNull(localCheck.getMultiple())) {
			    			bean.setMultiple(localCheck.getMultiple());
			    		}else {
			    			bean.setMultiple("35");
			    			localCheck.setMultiple("35");
			    		}
			            localCheckMapper.updateByPrimaryKeySelective(localCheck);
			         	localCheck.getIsIllegalactCode();
			         	if(task != null){
			         		if(!ChangnengUtil.isNull(task.getTaskFromType()) && task.getTaskFromType()!=1 && task.getTaskFromType()!=4){
			         			bean.setTitle("专项检查表");
			         			//专项行动
			         		    chickItemItem = JacksonUtils.toCollection(chickItemList, new TypeReference<List<LocalCheckItem>>() {});
			         			if(chickItemItem != null){
									for(LocalCheckItem list : chickItemItem ){
										if(list != null){
											list.setTaskId(taskId);
											list.setUpdateTime(new Date());
											if("7".equals(list.getCheckItemStatus())){
												if("0".equals(list.getDateType())){
													//精确到日期
													if(!ChangnengUtil.isNull(list.getEndDateStr())){
														list.setEndDate(DateUtil.parse(list.getEndDateStr(),"yyyy-MM-dd"));
													}
													if(!ChangnengUtil.isNull(list.getStartDateStr())){
														list.setStartDate(DateUtil.parse(list.getStartDateStr(),"yyyy-MM-dd"));
													}

												}else if ("1".equals(list.getDateType())){
													//精确到时间
													if(!ChangnengUtil.isNull(list.getEndDateStr())){
														list.setEndDate(DateUtil.parse(list.getEndDateStr(),"yyyy-MM-dd HH:mm:ss"));
													}
													if(!ChangnengUtil.isNull(list.getStartDateStr())){
														list.setStartDate(DateUtil.parse(list.getStartDateStr(),"yyyy-MM-dd HH:mm:ss"));
													}

												}
											}
											localCheckItemMapper.updateByPrimaryKeySelective(list);
										}
					         		}
								}
			         		}else{
			         			//根据任务的id查询检查项集合
			         			localCheckItemMapper.deleteByLocalCheckId(localCheck.getId());
			         		    chickItemItem = JacksonUtils.toCollection(chickItemList, new TypeReference<List<LocalCheckItem>>() {});
			         			if(chickItemItem != null){
			         				//选项表添加现场检查项信息id

			         				int i=0;
									for(LocalCheckItem list : chickItemItem ){
										if(list != null){
											list.setId(null);
											list.setTaskId(taskId);
											list.setLoction(i++);
											list.setCreateTime(new Date());
											list.setUpdateTime(new Date());
											list.setLocalCheckId(localCheck.getId());

											// 确保原有检查项的formType设置为0
											if(list.getFormType() == null) {
												list.setFormType(0);
											}
											if("7".equals(list.getCheckItemStatus())){
												if("0".equals(list.getDateType())){
													//精确到日期
													if(!ChangnengUtil.isNull(list.getEndDateStr())){
														list.setEndDate(DateUtil.parse(list.getEndDateStr(),"yyyy-MM-dd"));
													}
													if(!ChangnengUtil.isNull(list.getStartDateStr())){
														list.setStartDate(DateUtil.parse(list.getStartDateStr(),"yyyy-MM-dd"));
													}

												}else if ("1".equals(list.getDateType())){
													//精确到时间
													if(!ChangnengUtil.isNull(list.getEndDateStr())){
														list.setEndDate(DateUtil.parse(list.getEndDateStr(),"yyyy-MM-dd HH:mm:ss"));
													}
													if(!ChangnengUtil.isNull(list.getStartDateStr())){
														list.setStartDate(DateUtil.parse(list.getStartDateStr(),"yyyy-MM-dd HH:mm:ss"));
													}

												}
											}
											localCheckItemMapper.insertSelective(list);
											if("".equals(list.getCheckItemResult()) ||list.getCheckItemResult() ==null){
												list.setCheckItemResult("无");
											}
											if("1".equals(list.getCheckItemResult())){
												//是
												list.setCheckItemResult("是");
											}else if("0".equals(list.getCheckItemResult())){
												//否
												list.setCheckItemResult("否");
											}
										}
									}
			         			}

			         			chickItemItem = localCheckItemMapper.getChickItemItemByTaskId(taskId);
			        			bean.setTitle("现场检查表");
			         		}
			         	}
			         	//封装pdf文件
			         	if(chickItemItem != null && chickItemItem.size()>0){
			         		for(LocalCheckItem list : chickItemItem ){
			         			if("".equals(list.getCheckItemResult()) ||list.getCheckItemResult() ==null){
			         				list.setCheckItemResult("无");
			         			}
			         			if("1".equals(list.getCheckItemResult())){
			         				//是
			         				list.setCheckItemResult("是");
			         			}else if("0".equals(list.getCheckItemResult())){
			         				//否
			         				list.setCheckItemResult("否");
			         			}
			         			if("7".equals(list.getCheckItemStatus())){
			         				//时间段
			         				String startDate = null;
			         				String endDate = null;
			         				if("0".equals(list.getDateType())){
			         					if(!ChangnengUtil.isNull(list.getStartDate())){
			         						startDate = DateUtil.format(list.getStartDate(), "yyyy-MM-dd");
			         					}
			         					if(!ChangnengUtil.isNull(list.getEndDate())){
			         						endDate = DateUtil.format(list.getEndDate(), "yyyy-MM-dd");
			         					}
			         				}else{
			         					if(!ChangnengUtil.isNull(list.getStartDate())){
			         						startDate = DateUtil.format(list.getStartDate(), "yyyy-MM-dd HH:mm:ss");
			         					}
			         					if(!ChangnengUtil.isNull(list.getEndDate())){
			         						endDate = DateUtil.format(list.getEndDate(), "yyyy-MM-dd HH:mm:ss");
			         					}
			         				}
			         				list.setCheckItemResult(startDate+","+endDate);
			         			}
			         		}
			         	}
			         	bean.setListItem(chickItemItem);
			         	//生成pdf文件
			         	bean.setBackgroundImg(ResourceLoader.getPdfBackgroundImg("pdf_locakExamine.jpg"));
						String generateLocalCheckPdf = generatePdfService.generateLocalCheckPdf(bean, File.separator+"pdfConfig"+File.separator+"templet"+File.separator+"localExamineModel.html");
						//上传dfs
						String url = fd.uploadFile(generateLocalCheckPdf);
						if(!"".equals(url)&& url != null){
                        	//上传成功修改现场检查表的url信息
                        	localCheckMapper.updateDocUrlByLocalCheck(url,localCheck.getId());
                        	 if(!"".equals(docUrl) && docUrl != null){
        			        	 //fd.delete_file(docUrl.substring(0,docUrl.indexOf("/")), docUrl.substring(7));
        			         }
						}else{
                        	logger.info("上传fastdfs失败，url为空！");
                        	 throw new BusinessException("上传fastdfs失败，url为空！");
                        }
        				return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.UPDATE_SUCCESS.toString(), "保存成功！", "保存成功！", null);
			}else{
				//保存操作
				if(!ChangnengUtil.isNull(chickItemList)){
					List<LocalCheckItem> chickItemItem = JacksonUtils.toCollection(chickItemList, new TypeReference<List<LocalCheckItem>>() {
					});
						localCheck.setSaveStatus("1");
						localCheck.setCreatUserName(sysUser.getLoginname());
						localCheck.setCreatUserId(sysUser.getId());
						//行距倍数
						if(!ChangnengUtil.isNull(localCheck.getMultiple())) {
							bean.setMultiple(localCheck.getMultiple());
						}else {
							bean.setMultiple("35");
							localCheck.setMultiple("35");
						}
					localCheckMapper.insertSelective(localCheck);
					String localCheckId = localCheck.getId();//4D9630AC21236D1DE055000000000001
					bean.setListItem(chickItemItem);
					if(task != null){
						if(!ChangnengUtil.isNull(task.getTaskFromType()) && task.getTaskFromType()!=1&& task.getTaskFromType()!=4){
							bean.setTitle("专项检查表");
						}else{
							bean.setTitle("现场检查表");
						}
					}
					if(chickItemItem != null){
						//选项表添加现场检查项信息id
						int i=0;
						for(LocalCheckItem list : chickItemItem ){
							if(list != null){
								if("7".equals(list.getCheckItemStatus())){
									//时间段
									list.getEndDateStr();
									list.getStartDateStr();
									if("7".equals(list.getCheckItemStatus())){
									if("0".equals(list.getDateType())){
										//精确到日期
										if(!ChangnengUtil.isNull(list.getEndDateStr())){
											list.setEndDate(DateUtil.parse(list.getEndDateStr(),"yyyy-MM-dd"));
										}
										if(!ChangnengUtil.isNull(list.getStartDateStr())){
											list.setStartDate(DateUtil.parse(list.getStartDateStr(),"yyyy-MM-dd"));
										}

									}else if ("1".equals(list.getDateType())){
										//精确到时间
										if(!ChangnengUtil.isNull(list.getEndDateStr())){
											list.setEndDate(DateUtil.parse(list.getEndDateStr(),"yyyy-MM-dd HH:mm:ss"));
										}
										if(!ChangnengUtil.isNull(list.getStartDateStr())){
											list.setStartDate(DateUtil.parse(list.getStartDateStr(),"yyyy-MM-dd HH:mm:ss"));
										}
									}
								}
								}
								list.setId(null);
								list.setTaskId(taskId);
								list.setCreateTime(new Date());
								list.setUpdateTime(new Date());
								list.setLoction(i++);
								list.setLocalCheckId(localCheckId);
								list.setSceneSysItemId(list.getSceneSysItemId());
								localCheckItemMapper.insertSelective(list);
								if("".equals(list.getCheckItemResult()) ||list.getCheckItemResult() ==null){
									list.setCheckItemResult("无");
								}
								if("1".equals(list.getCheckItemResult())){
									//是
									list.setCheckItemResult("是");
								}else if("0".equals(list.getCheckItemResult())){
									//否
									list.setCheckItemResult("否");
								}
								if("7".equals(list.getCheckItemStatus())){
			         				//时间段
			         				String startDate = null;
			         				String endDate = null;
			         				if("0".equals(list.getDateType())){
			         					if(!ChangnengUtil.isNull(list.getStartDate())){
			         						startDate = DateUtil.format(list.getStartDate(), "yyyy-MM-dd");
			         					}
			         					if(!ChangnengUtil.isNull(list.getEndDate())){
			         						endDate = DateUtil.format(list.getEndDate(), "yyyy-MM-dd");
			         					}
			         				}else{
			         					if(!ChangnengUtil.isNull(list.getStartDate())){
			         						startDate = DateUtil.format(list.getStartDate(), "yyyy-MM-dd HH:mm:ss");
			         					}
			         					if(!ChangnengUtil.isNull(list.getEndDate())){
			         						endDate = DateUtil.format(list.getEndDate(), "yyyy-MM-dd HH:mm:ss");
			         					}
			         				}
			         				list.setCheckItemResult(startDate+","+endDate);
			         			}
							}
						}
					}
				}
		         	bean.setBackgroundImg(ResourceLoader.getPdfBackgroundImg("pdf_locakExamine.jpg"));
					String generateLocalCheckPdf = generatePdfService.generateLocalCheckPdf(bean, File.separator+"pdfConfig"+File.separator+"templet"+File.separator+"localExamineModel.html");
					String url = fd.uploadFile(generateLocalCheckPdf);
					if(!"".equals(url)&& url != null){
                    	//上传成功修改现场检查表的url信息
                    	localCheckMapper.updateDocUrlByLocalCheck(url,localCheck.getId());
					}else{
                    	 logger.info("上传fastdfs失败，url为空！");
                    	 throw new BusinessException("上传fastdfs失败，url为空！");
                    }
					return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.SAVE_SUCCESS.toString(), "保存成功！", "保存成功！", localCheck.getId());
			}
		} catch (Exception e) {
			 e.printStackTrace();
			 throw e;
		}
	}

	//--↓↓↓↓↓↓↓↓↓↓---
	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseJson saveLocalExamine(LocalCheck localCheck, String taskId, String chickItemList,
			SysUsers sysUser, List<SysFiles> filesList, Integer formType, String envSupervisionData) throws Exception {

		ResponseJson json = new ResponseJson();

		try {
			// 设置表单类型
			if (formType != null) {
				localCheck.setFormType(formType);
			} else {
				localCheck.setFormType(0); // 默认为原有检查项
			}

			// 调用原有的保存逻辑
			ResponseJson originalResult = saveLocalExamine(localCheck, taskId, chickItemList, sysUser, filesList);

			// 如果是环境监管一件事表单，保存相关数据
			if (Integer.valueOf(1).equals(formType) && envSupervisionData != null && !envSupervisionData.trim().isEmpty()) {
				String localCheckId = localCheck.getId();
				if (localCheckId == null) {
					// 从返回结果中获取ID
					@SuppressWarnings("unchecked")
					Map<String, Object> data = (Map<String, Object>) originalResult.getData();
					if (data != null && data.containsKey("localCheckId")) {
						localCheckId = (String) data.get("localCheckId");
					}
				}

				if (localCheckId != null) {
					saveEnvSupervisionItems(localCheckId, envSupervisionData);
				}
			}

			return originalResult;

		} catch (Exception e) {
			logger.error("保存现场检查表失败", e);
			throw new BusinessException("保存现场检查表失败: " + e.getMessage());
		}
	}
	//----------↑↑↑↑↑↑-----



	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseJson appSaveLocalExamine(LocalCheck localCheck,String taskId ,String chickItemList,SysUsers sysUser)throws Exception {
		//维护污染源数据
		//SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		LawEnforceObjectWithBLOBs oldBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(localCheck.getLawObjectId());
		LawEnforceObjectWithBLOBs newBLOBs = new LawEnforceObjectWithBLOBs();
		LocalCheckPDFBean bean = new LocalCheckPDFBean();
		String isIllegalactCode = localCheck.getIsIllegalactCode();
		FastDFSClient fd=new FastDFSClient("classpath:fdfs_client.conf");
		try {
		if(oldBLOBs != null ){
			BeanUtils.copyProperties(oldBLOBs, newBLOBs);
			localCheck.getAddress();
			String legalPerson = localCheck.getLegalPerson();
			String legalPhone = localCheck.getLegalPhone();
			if("1".equals(oldBLOBs.getTypeCode()) ){
				//企业类型修改污染源日志
				if(!ChangnengUtil.isNull(legalPerson) && !ChangnengUtil.isNull(legalPhone)){
					lawEnforceObjectMapper.updateLocalExamine(localCheck.getLawObjectId(),legalPerson,legalPhone);
				}
			}
			//污染源日志的记录
			newBLOBs.setLegalPerson(legalPerson);
			newBLOBs.setLegalPhone(legalPhone);
			SysDepartment sysDepartment = sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId());
			sysUser.setBelongDepartmentName(sysDepartment.getDepartmentName());
			List<LawobjUpdateLog> contrastLawObjectModified = CompareObjModified.contrastLawObjectModified(oldBLOBs, newBLOBs, sysUser);
			if(contrastLawObjectModified.size()>0){
				lawobjUpdateLogMapper.insertSelectiveList(contrastLawObjectModified);
			}
		}
		 //修改污染源信息
		Date checkStartDate = localCheck.getCheckStartDate();
		Date checkEndDate = localCheck.getCheckEndDate();
		String checkEndDateStr = null;
		String checkStartDateStr = null ;
		if(checkEndDate != null){
			 checkEndDateStr =DateUtil.getSimpleFormate(checkEndDate);
		}
		if(checkStartDate != null){
			 checkStartDateStr = DateUtil.getSimpleFormate(checkStartDate);
		}
		if(localCheck.getcheckUserIds()!= null && !"".equals(localCheck.getcheckUserIds())){
			String[] arr  = localCheck.getcheckUserIds().split(",");
			bean.setCheckUserLength(arr.length);
		}
		//格式化输出时间
		if(!"".equals(checkStartDateStr) && checkStartDateStr != null){
			bean.setStartYear(checkStartDateStr.substring(0, 4));
			bean.setStartMouth(checkStartDateStr.substring(5, 7));
			bean.setStartDay(checkStartDateStr.substring(8, 11));
			bean.setStartHour(checkStartDateStr.substring(11, 13));
			bean.setStartMinute(checkStartDateStr.substring(14));
		}
		if(!"".equals(checkEndDateStr) && checkEndDateStr != null){
			bean.setEndYear(checkEndDateStr.substring(0, 4));
			bean.setEndMouth(checkEndDateStr.substring(5, 7));
			bean.setEndDay(checkEndDateStr.substring(8, 11));
			bean.setEndHour(checkEndDateStr.substring(11, 13));
			bean.setEndMinute(checkEndDateStr.substring(14));
		}
			//获取部门信息
			String queryHBJNameByAreacode = surveyRecordMapper.queryHBJNameByAreacode(sysUser.getBelongAreaId());
			bean.setName(queryHBJNameByAreacode);
			String makeUnitName = localCheck.getMakeUnitName();
			if(ChangnengUtil.isNull(makeUnitName)){
				bean.setName(queryHBJNameByAreacode);
			}else{
				bean.setName(makeUnitName);
			}
			bean.setAddress(localCheck.getAddress());
			bean.setObjectName(localCheck.getObjectName());
			bean.setLegalPerson(localCheck.getLegalPerson());
			bean.setcheckUserNames(localCheck.getcheckUserNames());;
			bean.setLawEnforcIds(localCheck.getLawEnforcIds());;
			bean.setLocalPerson(localCheck.getLocalPerson());
			bean.setLegalPhone(localCheck.getLegalPhone());
			bean.setLocalPersonJob(localCheck.getLocalPersonJob());
			bean.setInformDeptName(localCheck.getInformDeptName());
			bean.setInformLawIds(localCheck.getInformLawIds());
			bean.setParticipant(localCheck.getParticipant());//参与人员
			if(!ChangnengUtil.isNull(localCheck.getCheckSummary())){
				bean.setCheckSummary(localCheck.getCheckSummary().replace("\n","<br/>"));
			}
			bean.setLocalPersonPhone(localCheck.getLocalPersonPhone());
			localCheck.setLastUpdateDate(new Date());
			Task task = jcblService.taskByTaskId(taskId);
			if(!ChangnengUtil.isNull(localCheck.getId())){
				List<LocalCheckItem> chickItemItem  = null;
					//修改操作
					//删除fdfs中的文件
			         String docUrl = localCheck.getDocUrl();
			         	localCheckMapper.updateByPrimaryKeySelective(localCheck);
			         	localCheck.getIsIllegalactCode();
			         	//维护是否存在违法行为名称:是，否，疑似 任务task表 V2.0.3 放弃维护这个字段
			         	//taskMapper.updateTaskisIllegalactCode(localCheck.getIsIllegalactCode(),taskId);
			         	if(task != null){
			         		if(!ChangnengUtil.isNull(task.getTaskFromType()) && task.getTaskFromType()!=1&& task.getTaskFromType()!=4){
			         			bean.setTitle("专项检查表");
			         			//专项行动
			         			if(!ChangnengUtil.isNull(chickItemList)){
			         				chickItemItem = JacksonUtils.toCollection(chickItemList, new TypeReference<List<LocalCheckItem>>() {});
			         				if(chickItemItem != null){
			         					for(LocalCheckItem list : chickItemItem ){
			         						if(list != null){
			         							list.setTaskId(taskId);
			         							list.setUpdateTime(new Date());
			         							localCheckItemMapper.updateByPrimaryKeySelective(list);
			         						}
			         					}
			         				}
			         			}
			         		}else{
			         			//根据任务的id查询检查项集合
			         			chickItemItem = localCheckItemMapper.getChickItemItemByTaskId(taskId);
			        			bean.setTitle("现场检查表");
			         		}
			         	}
			         	//封装pdf文件
			         	if(chickItemItem != null && chickItemItem.size()>0){
			         		for(LocalCheckItem list : chickItemItem ){
			         			if("".equals(list.getCheckItemResult()) ||list.getCheckItemResult() ==null){
			         				//list.setCheckItemResult("<span style='margin-left: 5px;width:15px;hight:18;border:1px solid black;display:inline-block;'>是  </span><span style='margin-left: 5px;width:15px;border:1px solid black;display:inline-block;'>否  </span>");
			         				list.setCheckItemResult("无");
			         			}
			         			if("1".equals(list.getCheckItemResult())){
			         				//是
			         				//list.setCheckItemResult("<span  style='margin-left: 5px;width:15px;hight:18;border:1px solid black;display:inline-block;background-color:#c3c3c3;'>是  </span><span style='margin-left: 5px;width:15px;border:1px solid black;display:inline-block;'>否  </span>");
			         				list.setCheckItemResult("是");
			         			}else if("0".equals(list.getCheckItemResult())){
			         				//否
			         				list.setCheckItemResult("否");
			         				//list.setCheckItemResult("<span style='margin-left: 5px;width:15px;hight:18;border:1px solid black;display:inline-block;'>是  </span><span style='margin-left: 5px;width:15px;border:1px solid black;display:inline-block;background-color:#c3c3c3;'>否  </span>");
			         			}
			         		}
			         	}
			         	bean.setListItem(chickItemItem);
			         	//生成pdf文件
			         	bean.setBackgroundImg(ResourceLoader.getPdfBackgroundImg("pdf_locakExamine.jpg"));
						String generateLocalCheckPdf = generatePdfService.generateLocalCheckPdf(bean, File.separator+"pdfConfig"+File.separator+"templet"+File.separator+"localExamineModel.html");
						//上传dfs
						String url = fd.uploadFile(generateLocalCheckPdf);
						if(!"".equals(url)&& url != null){
                        	//上传成功修改现场检查表的url信息
                        	localCheckMapper.updateDocUrlByLocalCheck(url,localCheck.getId());
                        	 if(!"".equals(docUrl) && docUrl != null){
        			        	 //fd.delete_file(docUrl.substring(0,docUrl.indexOf("/")), docUrl.substring(7));
        			         }
						}else{
                        	logger.info("上传fastdfs失败，url为空！");
                        	 throw new BusinessException("上传fastdfs失败，url为空！");
                        }
        				return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.UPDATE_SUCCESS.toString(), "保存成功！", "保存成功！", null);
			}else{
				//保存操作
					List<LocalCheckItem> chickItemItem = JacksonUtils.toCollection(chickItemList, new TypeReference<List<LocalCheckItem>>() {
					});
					localCheckMapper.insertSelective(localCheck);
					String localCheckId = localCheck.getId();//4D9630AC21236D1DE055000000000001
					bean.setListItem(chickItemItem);
				 	if(task != null){
		         		if(!ChangnengUtil.isNull(task.getTaskFromType()) && task.getTaskFromType()!=1 && task.getTaskFromType()!=4){
		         			bean.setTitle("专项检查表");
		         		}else{
		        			bean.setTitle("现场检查表");
		         		}
		         	}
					if(chickItemItem != null){
						//选项表添加现场检查项信息id
						for(LocalCheckItem list : chickItemItem ){
							if(list != null){
								list.setId(null);
								list.setTaskId(taskId);
								list.setCreateTime(new Date());
								list.setUpdateTime(new Date());
								list.setLocalCheckId(localCheckId);
								localCheckItemMapper.insertSelective(list);
								if("".equals(list.getCheckItemResult()) ||list.getCheckItemResult() ==null){
									//list.setCheckItemResult("<span style='width:16px;border:1px solid black;'>是</span><span style='width:16px;border:1px solid black'>否</span>");
									list.setCheckItemResult("无");
								}
								if("1".equals(list.getCheckItemResult())){
									//是
									list.setCheckItemResult("是");
									//list.setCheckItemResult("<span class ='icon-ok' style='width:16px;border:1px solid black;background-color:#c3c3c3;'>是</span><span style='width:16px;border:1px solid black'>否</span>");
								}else if("0".equals(list.getCheckItemResult())){
									//否
									list.setCheckItemResult("否");
									//list.setCheckItemResult("<span style='width:16px;border:1px solid black;'>是</span><span  class ='icon-ok' style='width:16px;border:1px solid black;background-color:#c3c3c3;'>否</span>");
								}
							}
		         		}
					}

		         	bean.setBackgroundImg(ResourceLoader.getPdfBackgroundImg("pdf_locakExamine.jpg"));
					String generateLocalCheckPdf = generatePdfService.generateLocalCheckPdf(bean, File.separator+"pdfConfig"+File.separator+"templet"+File.separator+"localExamineModel.html");
					String url = fd.uploadFile(generateLocalCheckPdf);
					if(!"".equals(url)&& url != null){
                    	//上传成功修改现场检查表的url信息
                    	localCheckMapper.updateDocUrlByLocalCheck(url,localCheck.getId());
					}else{
                    	logger.info("上传fastdfs失败，url为空！");
                    	 throw new BusinessException("上传fastdfs失败，url为空！");
                    }
					return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.SAVE_SUCCESS.toString(), "保存成功！", "保存成功！", localCheck.getId());
			}
		} catch (Exception e) {
			 e.printStackTrace();
			 throw new BusinessException("操作失败！，添加修改现场检查表失败！");
		}
	}


	@Override
	public List<LocalCheckItem>  getChickItemItem(LawObjectTypeBean lawObj,String localChickId) throws Exception {
		if(localChickId != null &&!"".equals(localChickId)){
			//在现在检查表中存在检查项信息直接加载
			String taskId = lawObj.getTaskId();
			if(taskId != null && !"".equals(taskId)){
				List<LocalCheckItem>  localCheckItem =	localCheckItemMapper.getChickItemItem(taskId,localChickId);
				return localCheckItem;
			}else{
				throw new BusinessException("localChickId id不存在");
			}
		}else{
		}
		return null;
	}

	@Override
	public ResponseJson checkItemChoose(String localCheckitemId, String status) throws BusinessException {

		if("0".equals(status)){
			//是操作
			localCheckItemMapper.updateByLocalCheckItemId(localCheckitemId,"0");

		}else if("1".equals(status)){
			//否操作
			localCheckItemMapper.updateByLocalCheckItemId(localCheckitemId,"1");
		}else{
			logger.info("检查项的是否操作状态为空");
			throw new BusinessException("检查项的是否操作状态为空");
		}
		return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.UPDATE_SUCCESS.toString(), "修改成功！", "修改成功！", null);
	}

	@Override
	public ResponseJson delCheckItem(String localCheckitemId) throws Exception {
		try {
			localCheckItemMapper.deleteByPrimaryKey(localCheckitemId);
		 	return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.DELETE_SUCCESS.toString(), "删除成功！", "删除成功！", null);
		} catch (Exception e) {
			logger.info("删除失败");
			throw new BusinessException("删除失败");
		}
	}

	@Override
	public ResponseJson editRemarkCheckItem(String localCheckitemId,
			String remarkCheckItemText) throws Exception {
		try {
			localCheckItemMapper.updateRemarkCheckItemById(localCheckitemId,remarkCheckItemText);
         	return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.UPDATE_SUCCESS.toString(), "修改备注成功！", "修改备注成功！", null);

		} catch (Exception e) {
			logger.info("修改备注失败");
			throw new BusinessException("修改备注失败");
		}
	}

	@Override
	public ResponseJson saveCheckItem(String templateObjectType ,String localCheckitem,
			String taskId,String localCheakId,String behId,String behFact) throws Exception {
		ResponseJson json  =new ResponseJson();
		try {
			List<LocalCheckItem> chickItemItem = localCheckItemMapper.getChickItemItem(taskId, localCheakId);
			LocalCheckItem localCheckItem = new LocalCheckItem();
			localCheckItem.setCheckItemName(localCheckitem);
			localCheckItem.setTaskId(taskId);
			localCheckItem.setLocalCheckId(localCheakId);
			localCheckItem.setCreateTime(new Date());
			localCheckItem.setCheckItemResult("1");
			localCheckItem.setUpdateTime(new Date());
			localCheckItem.setBehId(behId);
			localCheckItem.setBehFact(behFact);

			// 设置表单类型为原有检查项
			localCheckItem.setFormType(0);
			// 对于原有检查项，configItemId可以为空或根据业务需求设置
			// localCheckItem.setConfigItemId(null);

			if(chickItemItem ==null){
				localCheckItem.setLoction(1);
			}else{
				localCheckItem.setLoction(chickItemItem.size()+1);
			}
			//localCheckItem.setLoction(chickItemItem.size()+1);
			localCheckItem.setCheckItemStatus("0");
			localCheckItemMapper.insertSelective(localCheckItem );
			String localCheckitemId = localCheckItem.getId();
			Map<String ,String > map = new HashMap<String, String>();
			map.put("localCheckitemId", localCheckitemId);
			map.put("id", localCheckItem.getId());
			map.put("checkItemName", localCheckItem.getCheckItemName());
			map.put("checkItemResult", localCheckItem.getCheckItemResult());
			map.put("taskId", localCheckItem.getTaskId());
			map.put("localCheckId", localCheckItem.getLocalCheckId());
			map.put("sceneItemDatabaseId", localCheckItem.getSceneCustomItemId());
			map.put("sceneCustomItemId", localCheckItem.getSceneCustomItemId());
			map.put("loction", localCheckItem.getLoction().toString());
			map.put("behId", localCheckItem.getBehId());
			map.put("behFact", localCheckItem.getBehFact());
			map.put("checkItemStatus", localCheckItem.getCheckItemStatus());
			map.put("checkitemType", localCheckItem.getCheckitemType());
			map.put("formType", localCheckItem.getFormType() != null ? localCheckItem.getFormType().toString() : "0");
			map.put("configItemId", localCheckItem.getConfigItemId());
			//map.put("sceneItemDatabaseId",record.getId());
			json.success(HttpStatus.OK.toString(),SystemStatusCode.SAVE_SUCCESS.toString(), "保存检查项成功！","保存检查项成功!",map);
		} catch (Exception e) {
			logger.info("保存检查项失败");
			throw new BusinessException("保存检查项失败");
		}
		return json;
	}


	@Override
	public LocalCheck getLocalCheickItem(LawObjectTypeBean lawObj) {

		return localCheckMapper.getLocalCheickItem(lawObj.getTaskId());
	}

	@Override
	public ResponseJson getDocUrlByLocalCheckId(String localCheckId) {
		if(!"".equals(localCheckId) && localCheckId !=null){
			LocalCheck localCheck = localCheckMapper.selectByPrimaryKey(localCheckId);

			return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.UPDATE_SUCCESS.toString(), "修改备注成功！","修改备注成功",localCheck.getDocUrl());
		}else{
			logger.info("现场检查表主键的id为空！");
			return null;
		}
	}

	@Override
	public LocalCheck findDocUrlByLocalCheckId(String localCheckId) {
		return  localCheckMapper.selectByPrimaryKey(localCheckId);
}

	@Override
	public LocalCheck getLocalCheckDocurl(String localCheckId, int type) {
		LocalCheck record  = localCheckMapper.selectByPrimaryKey(localCheckId);
		if(type==1){
			// 这里我想用fastdfs读到nginx的代理地址，但这没有想好解决办法，这里先写在静态类里把
			if(record.getDocUrl()!=null && !"".equals(record.getDocUrl())){
				record.setDocUrl(PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs")+record.getDocUrl());
			}
		}
		return record;
	}

	@Override
	public ResponseJson checkItemRemark(String localCheckitemId) {
		ResponseJson json = new ResponseJson();
		LocalCheckItem localCheckItem = localCheckItemMapper.selectByPrimaryKey(localCheckitemId);
		Map<String,String> map = new HashMap<String,String>();
		map.put("remark", localCheckItem.getRemark());
		return json.success(HttpStatus.OK.toString(),SystemStatusCode.QUERY_SUCCESS.toString(), "查询检查项备注成功！！","查询检查项备注成功！",map);
	}

	@Override
	public List<ScanningAttachment> getAppendFile(String localCheckId) {
		return attachmentMapper.getAttByItemTypeAndCheckItemId("0", localCheckId);
	}

	@Override
	public LocalCheckItemBean checkItemList(String lawObjectType,String localCheakId,
			String status,String customModelerId,SysUsers sysUser) {
		LocalCheckItemBean localCheckItemBean = new LocalCheckItemBean();
		List<LocalCheckItem>   localCheckItemList = new ArrayList<LocalCheckItem>();
		if("1".equals(status)){
			if(!ChangnengUtil.isNull(localCheakId)){
				//检查项存在
				localCheckItemList =	localCheckItemMapper.getChickItemItem(null,localCheakId);
				localCheckItemBean.setContributionName(localCheckMapper.selectByPrimaryKey(localCheakId).getCreatUserName());
			}else{
				//新增操作
				//获取用户id下是否有默认模板
				//SceneDefaultModel sceneDefaultModel = sceneDefaultModelMapper.getSceneDefaultModel(sysUser.getId(),lawObjectType,"1");
				SceneDefaultModel sceneDefaultModel = sceneDefaultModelMapper.getUserSceneDefaultModel(sysUser.getId(),"1");
				if(sceneDefaultModel !=null){
					//查询贡献者
					localCheckItemBean.setContributionName(sceneCustomModelMapper.selectByPrimaryKey(sceneDefaultModel.getCustomDatabaseId()).getContributionName());
					//默认模板
					List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.sceneDefaultModelList(sceneDefaultModel.getCustomDatabaseId());
					for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
						if (sceneItemDatabase != null){
							LocalCheckItem localCheckItem = new LocalCheckItem();
							BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
							localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
							localCheckItemList.add(localCheckItem);
						}
					}
					localCheckItemBean.setType("(自定义模板)");
				}else{
					//系统只用一份，默认id为1
					//SceneDefaultModel sysSceneDefaultModel = sceneDefaultModelMapper.getSceneDefaultModel(sysUser.getId(),lawObjectType,"0");
					SceneDefaultModel sysSceneDefaultModel = sceneDefaultModelMapper.getUserSceneDefaultModel(sysUser.getId(),"0");
					if(sysSceneDefaultModel !=null){
						List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.sceneSysModelList(sysSceneDefaultModel.getCustomDatabaseId());
						for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
							LocalCheckItem localCheckItem = new LocalCheckItem();
							BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
							localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
							localCheckItemList.add(localCheckItem);
						}
					}else{
						List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.defaultSceneSysModelList();
						for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
							LocalCheckItem localCheckItem = new LocalCheckItem();
							BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
							localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
							localCheckItemList.add(localCheckItem);
						}
					}

					localCheckItemBean.setType("(系统模板)");
				}
			}
		}else if("0".equals(status)){
			//调用系统模板
			//系统只用一份，默认id为1
			List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.sceneSysModelList("1");
			for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
				LocalCheckItem localCheckItem = new LocalCheckItem();
				BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
				localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
				localCheckItemList.add(localCheckItem);
			}
			localCheckItemBean.setType("(系统模板)");
		}else if("2".equals(status)){
			//双击模板加载模板信息
			List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.sceneDefaultModelList(customModelerId);
			for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
				if (sceneItemDatabase != null){
					LocalCheckItem localCheckItem = new LocalCheckItem();
					BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
					localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
					localCheckItemList.add(localCheckItem);
				}
			}
			localCheckItemBean.setType("(自定义模板)");
		}
		if(localCheckItemList   != null){
			for(LocalCheckItem localCheckItem:localCheckItemList){
				if("1".equals(localCheckItem.getCheckItemStatus()) || "3".equals(localCheckItem.getCheckItemStatus())){
					//判断是否为下拉和多选；
					List<SceneCheckEntry> sceneCheckEntryList = sceneCheckEntryMapper.selectBySceneItemId(localCheckItem.getSceneItemDatabaseId());
					localCheckItem.setSceneCheckEntryList(sceneCheckEntryList);
				}
			}
		}

		localCheckItemBean.setList(localCheckItemList);
		return localCheckItemBean;
	}

	@Override
	public Task selectByTaskId(String taskId) {
		return taskMapper.selectByPrimaryKey(taskId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseJson saveTemplate(SceneCustomModel sceneCustomModel,SysUsers sysUser) throws Exception {
		ResponseJson json = new ResponseJson();
		Map<String, String> map = new HashMap<String, String>();
		try {
			//获取登录用户的信息
			SceneCustomModel SceneCustomModelTemp =	sceneCustomModelMapper.chickTemplateName(sceneCustomModel.getTemplateName(),sceneCustomModel.getTemplateObjectType());
			if(!ChangnengUtil.isNull(SceneCustomModelTemp)){
				map.put("tempStatus", "1");
				json.success(HttpStatus.OK.toString(),SystemStatusCode.QUERY_SUCCESS.toString(), "重复的模板名称,请更换模板名称！","重复的模板名称,请更换模板名称",map);
				return json;
			}
			//1类型为现象执法，2类型为勘察笔录，3类型为询问笔录
			String templateNumber =  systemCodingService.getSysTemplateCoding("XCJC");
			//模板编号
			sceneCustomModel.setTemplateNumber(templateNumber);
			//创建日期
			sceneCustomModel.setCreateTime(new Date());
			//最后更新时间
			sceneCustomModel.setUpdateTime(new Date());
			//模版创建人
			sceneCustomModel.setUserName(sysUser.getLoginname());
			//创建人ID
			sceneCustomModel.setUserId(sysUser.getId());
			//模版贡献者(获取上一个模板库的id查询起贡献者)
			if(ChangnengUtil.isNull(sceneCustomModel.getContributionName())){
				//贡献者为自己
				sceneCustomModel.setContributionName(sysUser.getLoginname());
			}else{
				//查询模板库贡献者
					String[] contributionIdArr = sceneCustomModel.getContributionName().split(",");
					//判断当前登录用户的id是否和贡献者的最后一个用户相同，相同不操作，不相同追加当前用户
					if(!sysUser.getLoginname().equals(contributionIdArr[contributionIdArr.length-1])){
						//追加
						sceneCustomModel.setContributionName(sceneCustomModel.getContributionName()+","+sysUser.getLoginname());
					}
				}
			//贡献者ID
			//保存自定义模板库
			sceneCustomModelMapper.insertSelective(sceneCustomModel);
			//保存检查项信息库
			String chickItemListTemp = sceneCustomModel.getChickItemListTemp();
			if(!ChangnengUtil.isNull(chickItemListTemp)){
				List<SceneItemDatabase> sceneItemDatabaseList = JacksonUtils.toCollection(chickItemListTemp, new TypeReference<List<SceneItemDatabase>>() {});
				for(SceneItemDatabase sceneItemDatabase :sceneItemDatabaseList){
					//查询模板项是否被删除
					SceneItemDatabase selectByPrimaryKey = sceneItemDatabaseMapper.selectByPrimaryKey(sceneItemDatabase.getSceneItemDatabaseId());
					if(ChangnengUtil.isNull(sceneItemDatabase.getSceneItemDatabaseId())  || selectByPrimaryKey == null){
							sceneItemDatabase.setCreateTime(new Date());
							sceneItemDatabase.setModelerType("1");
							sceneItemDatabase.setCheckItemResult("1");
							sceneItemDatabase.setTemplateObjectType(sceneCustomModel.getTemplateObjectType());
							sceneItemDatabaseMapper.insertSelective(sceneItemDatabase );
							//维护业务库的模板库id
							LocalCheckItem localCheckItem = new LocalCheckItem();
							localCheckItem.setId(sceneItemDatabase.getCheckItemId());
							localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
							localCheckItemMapper.updateByPrimaryKeySelective(localCheckItem);
							sceneItemDatabase.setSceneItemDatabaseId(sceneItemDatabase.getId());
						}
				//维护自定义模板检查项关联表
					SceneCustomItem SceneCustomItem = new SceneCustomItem(null, sceneCustomModel.getId(), sceneItemDatabase.getSceneItemDatabaseId(), sceneItemDatabase.getLoction());
					sceneCustomItemMapper.insertSelective(SceneCustomItem);
				}
			}
			//判断是否为常用模板 1是 0否
			String usuallyStatus = sceneCustomModel.getUsuallyStatus();
			if("1".equals(usuallyStatus)){
				SceneUsuallyModel sceneUsuallyModel = new SceneUsuallyModel(null,sysUser.getId(), sceneCustomModel.getId(), sceneCustomModel.getTemplateObjectType());
				sceneUsuallyModelMapper.insertSelective(sceneUsuallyModel);
			}
			//判断是否为默认模板
			String defaultStatus = sceneCustomModel.getDefaultStatus();
			if("1".equals(defaultStatus)){
				//删除原有类型的模板库
			//	sceneDefaultModelMapper.deleteSelectiveByIdAndType(sysUser.getId(),sceneCustomModel.getTemplateObjectType(),"1");
				sceneDefaultModelMapper.deleteSelectiveById(sysUser.getId(),"1");
				SceneDefaultModel  sceneDefaultModel = new SceneDefaultModel(null, sysUser.getId(),  sceneCustomModel.getId(), sceneCustomModel.getTemplateObjectType(),"1");
				sceneDefaultModelMapper.insertSelective(sceneDefaultModel);
			}
			map.put("tempStatus", "0");
			json.success(HttpStatus.OK.toString(),SystemStatusCode.SAVE_SUCCESS.toString(), "保存成功！","保存自定义模板成功",map);
		} catch (Exception e) {
			//map.put("tempStatus", "1");
			// json.success("200","200","保存自定义模板失败！",null,map);
		//	logger.info("自定义保存模板失败！");
			throw e;
		}
		return json;
	}

	@Override
	public PageBean<AskingCustomModel> localExamineCustomModeList(
			AskingCustomBean customBean) {
		String areaSql = "";
		if(customBean.getBelongCountry() == null || "".equals(customBean.getBelongCountry())){
			if(customBean.getBelongCity()!= null && !"".equals(customBean.getBelongCity())){
				areaSql = customBean.getBelongCity();
			}
		}else{
			areaSql = customBean.getBelongCountry();
		}
		if(areaSql!=null && !"".equals(areaSql)){
			if("000000".equals(areaSql.substring(2))){// 省
				customBean.setAreaSql(null);
			}else if("0000".equals(areaSql.substring(4))){// 市
				customBean.setAreaSql(" and scm.TEMPLATE_AREA like '"+areaSql.substring(0,4)+"%'  ");
			}else if ("00".equals(areaSql.substring(6))){// 县
				customBean.setAreaSql(" and scm.TEMPLATE_AREA = '"+areaSql+"' ");
			}else{
				customBean.setAreaSql(null);
			}
		}else{
			customBean.setAreaSql(null);
		}
		if(customBean.getPageNum()==null || customBean.getPageNum()==0 ){
			customBean.setPageNum(1);
		}
		if(customBean.getPageSize()==null || customBean.getPageSize()==0){
			customBean.setPageSize(10);
		}
		PageHelper.startPage(customBean.getPageNum(),customBean.getPageSize());
		return  new PageBean<AskingCustomModel>(sceneCustomModelMapper.getSceneCustomModelListByUser(customBean));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public JsonResult setUsuallyOrDefaultOrDelete(AskingCustomBean customBean) throws Exception {
		JsonResult jsonResult = new JsonResult();
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		//usuallyOrDefault： 0 常用/ 1 默认 / 2删除   code： 1->当前已经是常用 0->当前不是常用
		try {
			if(customBean.getUsuallyOrDefaultOrDelete()!=null && "0".equals(customBean.getUsuallyOrDefaultOrDelete())){ // 常用设置项
				if(customBean.getCode()!=null && "1".equals(customBean.getCode())){// 已经是常用设置为非常用
					sceneUsuallyModelMapper.deleteByPrimaryKeyByUser(sysUsers.getId(), customBean.getModelerId());
				}else if(customBean.getCode()!=null && "0".equals(customBean.getCode())){
					SceneUsuallyModel usuallyModel = new SceneUsuallyModel();
					usuallyModel.setUserid(sysUsers.getId());
					usuallyModel.setTemplateObjectType(customBean.getType());
					usuallyModel.setCustomDatabaseId(customBean.getModelerId());
					sceneUsuallyModelMapper.insertSelective(usuallyModel);
				}else{
					jsonResult.setResult(Const.RESULT_ERROR);
					jsonResult.setMessage("页面传递系是否为常用项信息错误");
					return jsonResult;
				}
			}else if(customBean.getUsuallyOrDefaultOrDelete()!=null && "1".equals(customBean.getUsuallyOrDefaultOrDelete())){ // 默认设置项
				if(customBean.getCode()!=null && "1".equals(customBean.getCode())){// 已经是默认设置为非默认
				//	sceneDefaultModelMapper.deleteByPrimaryKeyByUser(sysUsers.getId(), customBean.getModelerId(),"1");
					//sceneDefaultModelMapper.deleteByUser(sysUsers.getId(), "1");
					sceneDefaultModelMapper.deleteByPrimaryKeyByUserId(sysUsers.getId(),"1");
				}else if(customBean.getCode()!=null && "0".equals(customBean.getCode())){
					SceneDefaultModel defaultModel = new SceneDefaultModel();
					defaultModel.setUserid(sysUsers.getId());
					if(!ChangnengUtil.isNull(customBean.getType())){
						defaultModel.setTemplateObjectType(customBean.getType());
					}
					defaultModel.setTemplateType("1");
					defaultModel.setCustomDatabaseId(customBean.getModelerId());
					// 一个用户只能有一个默认的模版项，先删除之前的模版
					sceneDefaultModelMapper.deleteByPrimaryKeyByUserId(sysUsers.getId(),"1");
					// 新增
					sceneDefaultModelMapper.insertSelective(defaultModel);
				}else{
					jsonResult.setResult(Const.RESULT_ERROR);
					jsonResult.setMessage("页面传递系是否为默认项信息错误");
					return jsonResult;
				}
			}else if(customBean.getUsuallyOrDefaultOrDelete()!=null && "2".equals(customBean.getUsuallyOrDefaultOrDelete())){ // 创建者删除模版
				 // 常用表，默认表，模版表，模版与项中间表
				sceneUsuallyModelMapper.deleteByPrimaryKeyByCustomDatabaseId(customBean.getModelerId());
				sceneDefaultModelMapper.deleteByPrimaryKeyByCustomModelerId(customBean.getModelerId(),"1");
				sceneCustomItemMapper.deleteByPrimaryKeyByCustomModelerId(customBean.getModelerId());
				sceneCustomModelMapper.deleteByPrimaryKey(customBean.getModelerId());

			}else{
				jsonResult.setResult(Const.RESULT_ERROR);
				jsonResult.setMessage("页面传递系类型信息错误");
				return jsonResult;
			}
		} catch (Exception e) {
			throw e;
		}
		jsonResult.setResult(Const.RESULT_SUCCESS);
		jsonResult.setMessage("设置成功");
		return jsonResult;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public JsonResult saveItemContentLoction(ModelerLocationBean locationBean)throws Exception {
		JsonResult jsonResult = new JsonResult();
		if(ChangnengUtil.isNull(locationBean.getContentIdA()) || ChangnengUtil.isNull(locationBean.getContentIdB())){
			throw new BusinessException("移动数据无标志");
		}
		if(ChangnengUtil.isNull(locationBean.getIndex())){
			throw new BusinessException("移动下标不明确");
		}
		LocalCheckItem recordA = new LocalCheckItem();
		recordA.setId(locationBean.getContentIdA());
		LocalCheckItem recordB = new LocalCheckItem();
		recordB.setId(locationBean.getContentIdB());

		if("0".equals(locationBean.getType())){ // 上移
			recordA.setLoction(Integer.parseInt(locationBean.getIndex())-1);
			recordB.setLoction(Integer.parseInt(locationBean.getIndex())+1);
			localCheckItemMapper.updateByPrimaryKeySelective(recordA);
			localCheckItemMapper.updateByPrimaryKeySelective(recordB);
		}else if ("1".equals(locationBean.getType())){ // 下移
			recordA.setLoction(Integer.parseInt(locationBean.getIndex())+1);
			recordB.setLoction(Integer.parseInt(locationBean.getIndex())-1);
			localCheckItemMapper.updateByPrimaryKeySelective(recordA);
			localCheckItemMapper.updateByPrimaryKeySelective(recordB);

		}else{
			throw new BusinessException("类型不明确");
		}
		jsonResult.setResult(Const.RESULT_SUCCESS);
		return jsonResult;
	}

	@Override
	public LocalCheckItemBean checkItemList(LocalChickItemBean bean,SysUsers sysUser) throws Exception {
				LocalCheckItemBean localCheckItemBean = new LocalCheckItemBean();
				List<LocalCheckItem>   localCheckItemList = new ArrayList<LocalCheckItem>();
				if("1".equals(bean.getStatus())){
					if(!ChangnengUtil.isNull(bean.getLocalCheakId())){
						//检查项存在
						localCheckItemList =	localCheckItemMapper.getChickItemItem(null,bean.getLocalCheakId());
						if(localCheckItemList != null){
							for(LocalCheckItem list :localCheckItemList){
								if("1".equals(list.getCheckItemStatus()) ||"3".equals(list.getCheckItemStatus())){
									//判断是否为下拉和多选；
									List<SceneCheckEntry> sceneCheckEntryList = sceneCheckEntryMapper.selectBySceneItemId(list.getSceneItemDatabaseId());
									list.setSceneCheckEntryList(sceneCheckEntryList);
									//	List<TcDictionary> tcDictionaryList = jcblService.TcDictionaryList(list.getCheckitemType());
									//list.setTcList(tcDictionaryList);
								}
							}
						}
						localCheckItemBean.setContributionName(localCheckMapper.selectByPrimaryKey(bean.getLocalCheakId()).getCreatUserName());
					}else{
						//获取专项行动
 						if(!ChangnengUtil.isNull(bean.getTaskId())){
							Task task = jcblService.taskByTaskId(bean.getTaskId());
							if(task != null && !ChangnengUtil.isNull(task.getTaskFromType()) && task.getTaskFromType()!=1 && task.getTaskFromType()!=5 ){
								List<SceneItemDatabase> sysSpecialItem = sceneItemDatabaseMapper.SysSpecialItemByType(task.getTaskFromType().toString());
								if(sysSpecialItem != null && sysSpecialItem.size()>0){
									for(SceneItemDatabase sceneItemDatabase : sysSpecialItem){
										LocalCheckItem localCheckItem = new LocalCheckItem();
										sceneItemDatabase.setIsMust(sceneItemDatabase.getIsRequired());
										BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
										localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
										//去字典表查询对应的集合 0单选，1多选，2输入，3下拉
										if("1".equals(sceneItemDatabase.getCheckItemStatus()) ||"3".equals(sceneItemDatabase.getCheckItemStatus())){
											List<SceneCheckEntry> sceneCheckEntryList = sceneCheckEntryMapper.selectBySceneItemId(localCheckItem.getSceneItemDatabaseId());
											localCheckItem.setSceneCheckEntryList(sceneCheckEntryList);
											/*List<TcDictionary> tcDictionaryList = jcblService.TcDictionaryList(sceneItemDatabase.getCheckitemType());
											localCheckItem.setTcList(tcDictionaryList);*/
										}
										localCheckItemList.add(localCheckItem);
									}
								}
								localCheckItemBean.setList(localCheckItemList);
								localCheckItemBean.setContributionName("系统");
								//return localCheckItemBean;
							}else if(!ChangnengUtil.isNull(task.getTaskFromCode())&&task.getTaskFromCode().equals("8")&&!ChangnengUtil.isNull(task.getSpecialActionIds())){
								//2021.0508  是否双随机抽查  是否关联专项  且指定了检查表 有就显示指定检查表的检查项
								List<SceneItemDatabase> sceneItemDatabases = sceneItemDatabaseMapper.SysSpecialItemById(task.getSpecialActionIds());


								if(sceneItemDatabases.size()>0){
									for(SceneItemDatabase sceneItemDatabase : sceneItemDatabases){
										LocalCheckItem localCheckItem = new LocalCheckItem();
										BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
										localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
										localCheckItemList.add(localCheckItem);
									}
									localCheckItemBean.setType("(系统模板)");
								}else{
									List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.defaultSceneSysModelList();
									for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
										LocalCheckItem localCheckItem = new LocalCheckItem();
										BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
										localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
										localCheckItemList.add(localCheckItem);
									}
									localCheckItemBean.setType("(系统模板)");
								}


							}else  if(ChangnengUtil.isNull(task.getSpecialActionIds())){
							//新增操作
							//获取用户id下是否有默认模板
							//SceneDefaultModel sceneDefaultModel = sceneDefaultModelMapper.getSceneDefaultModel(sysUser.getId(),bean.getLawObjectType(),"1");
							SceneDefaultModel sceneDefaultModel = sceneDefaultModelMapper.getUserSceneDefaultModel(sysUser.getId(),"1");
							if(sceneDefaultModel !=null){
								//查询贡献者
								SceneCustomModel sceneCustomModel = sceneCustomModelMapper.selectByPrimaryKey(sceneDefaultModel.getCustomDatabaseId());
								if(sceneCustomModel!= null && sceneCustomModel.getContributionName() != null){
									localCheckItemBean.setContributionName(sceneCustomModel.getContributionName());
								}		//默认模板
								List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.sceneDefaultModelList(sceneDefaultModel.getCustomDatabaseId());
								for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
									if (sceneItemDatabase != null){
										LocalCheckItem localCheckItem = new LocalCheckItem();
										BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
										localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
										localCheckItemList.add(localCheckItem);
									}
								}
								localCheckItemBean.setType("(自定义模板)");
							}else{
								//获取自己系统模板
								//SceneDefaultModel sysSceneDefaultModel = sceneDefaultModelMapper.getSceneDefaultModel(sysUser.getId(),bean.getLawObjectType(),"0");
								SceneDefaultModel sysSceneDefaultModel = sceneDefaultModelMapper.getUserSceneDefaultModel(sysUser.getId(),"0");
								if(sysSceneDefaultModel !=null){
									//获取自己系统模板
									List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.sceneSysModelList(sysSceneDefaultModel.getCustomDatabaseId());
									for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
										LocalCheckItem localCheckItem = new LocalCheckItem();
										BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
										localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
										localCheckItemList.add(localCheckItem);
									}
									localCheckItemBean.setType("(系统模板)");
								}else{
									//系统只用一份，默认id为1
									List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.defaultSceneSysModelList();
									for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
										LocalCheckItem localCheckItem = new LocalCheckItem();
										BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
										localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
										localCheckItemList.add(localCheckItem);
									}
									localCheckItemBean.setType("(系统模板)");
								}
							}
						}
					}
					}
				//	return localCheckItemBean;
				}else if("0".equals(bean.getStatus())){
					//调用系统模板
					//系统只用一份，默认id为1
					List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.sceneSysModelList("1");
					for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
						LocalCheckItem localCheckItem = new LocalCheckItem();
						BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
						localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
						localCheckItemList.add(localCheckItem);
					}
					localCheckItemBean.setType("(系统模板)");
				}else if("2".equals(bean.getStatus())){
					//双击模板加载模板信息
					List<SceneItemDatabase> sceneDefaultModelList =  sceneItemDatabaseMapper.sceneDefaultModelList(bean.getCustomModelerId());
					for(SceneItemDatabase sceneItemDatabase : sceneDefaultModelList){
						if (sceneItemDatabase != null){
							LocalCheckItem localCheckItem = new LocalCheckItem();
							BeanUtils.copyProperties(sceneItemDatabase, localCheckItem);
							localCheckItem.setSceneItemDatabaseId(sceneItemDatabase.getId());
							localCheckItemList.add(localCheckItem);
						}
					}
					localCheckItemBean.setType("(自定义模板)");
				}
				if(localCheckItemList   != null){
					for(LocalCheckItem localCheckItem:localCheckItemList){
						List<Boolean> list = new ArrayList<Boolean>();
						if("1".equals(localCheckItem.getCheckItemStatus()) || "3".equals(localCheckItem.getCheckItemStatus())){
							//判断是否为下拉和多选；
							List<SceneCheckEntry> sceneCheckEntryList = sceneCheckEntryMapper.selectBySceneItemId(localCheckItem.getSceneItemDatabaseId());
							localCheckItem.setSceneCheckEntryList(sceneCheckEntryList);
							if("1".equals(localCheckItem.getCheckItemStatus()) ){
								if(!ChangnengUtil.isNull(localCheckItem.getCheckItemResult())){
									String[] split = localCheckItem.getCheckItemResult().split(",");
									for(int i=0;i<sceneCheckEntryList.size();i++){
										Boolean flag =false;
										for(String str  : split){
												if(str.equals(sceneCheckEntryList.get(i).getCheckEntryName()) ){
													flag =true;
												}
											}
										list.add(flag);
									}
								}else{
									for(int i=0;i<sceneCheckEntryList.size();i++){
										list.add(false);
									}
								}
							}
						}
						localCheckItem.setCheckboxResult(list);
						if("5".equals(localCheckItem.getCheckItemStatus())){
							//对经纬度存储 转换
							if(!ChangnengUtil.isNull(localCheckItem.getCheckItemResult()) && localCheckItem.getCheckItemResult().indexOf(",")!=-1){
								String[] split = localCheckItem.getCheckItemResult().split(",");
								if(	split != null && split.length>0){
									localCheckItem.setGisCoordinateX(split[0]);
									localCheckItem.setGisCoordinateY(split[1]);
								}
							}
						}
						if("7".equals(localCheckItem.getCheckItemStatus())){
							localCheckItem.getStartDate();
							localCheckItem.getEndDate();
							if("1".equals(localCheckItem.getDateType())){
								//精确到时间
								if(!ChangnengUtil.isNull(localCheckItem.getStartDate())){
									localCheckItem.setStartDateStr(DateUtil.format(localCheckItem.getStartDate(), "yyyy-MM-dd HH:mm:ss"));
								}
								if(!ChangnengUtil.isNull(localCheckItem.getEndDate())){
									localCheckItem.setEndDateStr(DateUtil.format(localCheckItem.getEndDate(), "yyyy-MM-dd HH:mm:ss"));
								}
							}else if("0".equals(localCheckItem.getDateType())){
								//精确到日期
								if(!ChangnengUtil.isNull(localCheckItem.getStartDate())){
									localCheckItem.setStartDateStr(DateUtil.format(localCheckItem.getStartDate(), "yyyy-MM-dd"));
								}
								if(!ChangnengUtil.isNull(localCheckItem.getEndDate())){
									localCheckItem.setEndDateStr(DateUtil.format(localCheckItem.getEndDate(), "yyyy-MM-dd"));
								}
							}
						}
					}
				}
				Iterator<LocalCheckItem> iterator = localCheckItemList.iterator();
				while(iterator.hasNext()) {
					LocalCheckItem next = iterator.next();
					if(next.getCheckItemResult()==null) {
						next.setCheckItemResult("");
					}
				}
				localCheckItemBean.setList(localCheckItemList);
				return localCheckItemBean;
	}

	@Override
	public ResponseJson tempSaveLocalExamine(LocalCheck localCheck,
			String taskId, String chickItemList, SysUsers sysUser) throws Exception {
				try {
				 //修改污染源信息
					if(!ChangnengUtil.isNull(localCheck.getId())){
						List<LocalCheckItem> chickItemItem  = null;
							//修改操作
								localCheck.setUpdateUserName(sysUser.getLoginname());
								localCheck.setUpdateUserId(sysUser.getId());
					         	localCheckMapper.updateByPrimaryKeySelective(localCheck);
					         	localCheck.getIsIllegalactCode();
					         	localCheck.setSaveStatus("0");

					         	//维护是否存在违法行为名称:是，否，疑似 任务task表 V2.0.3放弃维护这个字段
					         	//taskMapper.updateTaskisIllegalactCode(localCheck.getIsIllegalactCode(),taskId);
					         	Task task = jcblService.taskByTaskId(taskId);
					         	if(task != null){
					         		if(!ChangnengUtil.isNull(task.getTaskFromType()) && task.getTaskFromType() !=1){
					         			//专项行动
					         		    chickItemItem = JacksonUtils.toCollection(chickItemList, new TypeReference<List<LocalCheckItem>>() {});
					         			if(chickItemItem != null){
											for(LocalCheckItem list : chickItemItem ){
												if(list != null){
													list.setTaskId(taskId);
													list.setUpdateTime(new Date());
													if("7".equals(list.getCheckItemStatus())){
														if("0".equals(list.getDateType())){
															//精确到日期
															if(!ChangnengUtil.isNull(list.getEndDateStr())){
																list.setEndDate(DateUtil.parse(list.getEndDateStr(),"yyyy-MM-dd"));
															}
															if(!ChangnengUtil.isNull(list.getStartDateStr())){
																list.setStartDate(DateUtil.parse(list.getStartDateStr(),"yyyy-MM-dd"));
															}

														}else if ("1".equals(list.getDateType())){
															//精确到时间
															if(!ChangnengUtil.isNull(list.getEndDateStr())){
																list.setEndDate(DateUtil.parse(list.getEndDateStr(),"yyyy-MM-dd HH:mm:ss"));
															}
															if(!ChangnengUtil.isNull(list.getStartDateStr())){
																list.setStartDate(DateUtil.parse(list.getStartDateStr(),"yyyy-MM-dd HH:mm:ss"));
															}

														}
													}
													localCheckItemMapper.updateByPrimaryKeySelective(list);
												}
							         		}
										}else{
								         	//根据任务的id查询检查项集合
								         	  chickItemItem = localCheckItemMapper.getChickItemItemByTaskId(taskId);
										}
					         		}
					         	}
					         	return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.UPDATE_SUCCESS.toString(), "暂存成功！", "暂存成功！", localCheck.getId());
					}else{
						//保存操作
							List<LocalCheckItem> chickItemItem = JacksonUtils.toCollection(chickItemList, new TypeReference<List<LocalCheckItem>>() {
							});
							localCheck.setSaveStatus("0");
							localCheck.setCreatUserName(sysUser.getLoginname());
							localCheck.setCreatUserId(sysUser.getId());
							localCheckMapper.insertSelective(localCheck);
							String localCheckId = localCheck.getId();
							if(chickItemItem != null){
								for(LocalCheckItem list : chickItemItem ){
									if(list != null){
										list.setId(null);
										list.setTaskId(taskId);
										list.setCreateTime(new Date());
										list.setUpdateTime(new Date());
										list.setLocalCheckId(localCheckId);
										if("7".equals(list.getCheckItemStatus())){
											if("0".equals(list.getDateType())){
												//精确到日期
												if(!ChangnengUtil.isNull(list.getEndDateStr())){
													list.setEndDate(DateUtil.parse(list.getEndDateStr(),"yyyy-MM-dd"));
												}
												if(!ChangnengUtil.isNull(list.getStartDateStr())){
													list.setStartDate(DateUtil.parse(list.getStartDateStr(),"yyyy-MM-dd"));
												}

											}else if ("1".equals(list.getDateType())){
												//精确到时间
												if(!ChangnengUtil.isNull(list.getEndDateStr())){
													list.setEndDate(DateUtil.parse(list.getEndDateStr(),"yyyy-MM-dd HH:mm:ss"));
												}
												if(!ChangnengUtil.isNull(list.getStartDateStr())){
													list.setStartDate(DateUtil.parse(list.getStartDateStr(),"yyyy-MM-dd HH:mm:ss"));
												}

											}
										}
										localCheckItemMapper.insertSelective(list);
									}
				         		}
							}
							return new ResponseJson().success(HttpStatus.OK.toString(),SystemStatusCode.SAVE_SUCCESS.toString(), "暂存成功！", "暂存成功！", localCheck.getId());
					}
				} catch (Exception e) {
					 e.printStackTrace();
					 throw new BusinessException("操作失败！，添加修改现场检查表失败！");
				}
	}



	@Override
	public LocalCheck selectLocalItemByTaskId(String taskId) {
		return	localCheckMapper.selectLocalItemByTaskId(taskId);
	}


	@Override
	public PageBean<SceneSysModelResult> sysTemplateList(
			SceneSysModelSeach sceneSysModelSeach) {
		// 设置分页默认数
		if (ChangnengUtil.isNull(sceneSysModelSeach.getPageNumber())) {
			sceneSysModelSeach.setPageNumber(1);
		}
		if (ChangnengUtil.isNull(sceneSysModelSeach.getPageSize())) {
			sceneSysModelSeach.setPageSize(15);
		}
		List<SceneSysModelResult> sceneSysModelList = null;
		// 控制翻页
		PageHelper.startPage(sceneSysModelSeach.getPageNumber(), sceneSysModelSeach.getPageSize());
		try {
			// 获取系统模版信息
			sceneSysModelList = sceneSysModelMapper.selectSceneSysModelBySeachInfo(sceneSysModelSeach);
			//sceneSysModelList = sceneSysModelMapper.selectSceneSysModelBySeachInfo(sceneSysModelSeach);
		} catch (Exception e) {
			logger.info("系统模板列表获取失败");
			logger.info(e);
			e.printStackTrace();
		}
		return new PageBean<SceneSysModelResult>(sceneSysModelList);
	}



	@Override
	public List<SceneItemDatabase> sysTemplateList(String id) {
		//List<SceneItemDatabase> sceneSysModelList = sceneItemDatabaseMapper.sceneSysModelList(id);
		return sceneItemDatabaseMapper.sceneSysModelList(id);
	}



	@Override
	public List<SceneItemDatabase> customTemplateById(String id) {
		return sceneItemDatabaseMapper.sceneSysModelList(id);
	}

	@Override
	public List<SysFiles> uploadFiless(HttpServletRequest request, HttpServletResponse response, SysUsers sysUsers) throws BusinessException {
		List<SysFiles> list = null;
		try {
			// 创建一个通用的多部分解析器
			CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
					request.getSession().getServletContext());
			// 判断 request 是否有文件上传,即多部分请求
			if (multipartResolver.isMultipart(request)) {
				// 转换成多部分request
				MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
				// 取得request中的所有文件名
				Iterator<String> iter = multiRequest.getFileNames();
				while (iter.hasNext()) {
					// 取得上传文件
					List<MultipartFile> fileList = multiRequest.getFiles(iter.next());
					if (!fileList.isEmpty()) {
						list = new ArrayList<SysFiles>();
						for (MultipartFile mf : fileList) {
							//
							boolean fileAllowed = CheckFileFormatTools.isFileAllowed(mf);
							if(!fileAllowed){
								throw new Exception("文件格式有问题，请检查待上传文件是否能正常打开");
							}
							SysFiles sf = new SysFiles();
							// 取得当前上传文件的文件名称
							String myFileName = mf.getOriginalFilename();
							// 如果名称不为"",说明该文件存在，否则说明该文件不存在
							if (myFileName.trim() != "") {
								String prefix = myFileName.substring(myFileName.lastIndexOf(".") + 1);
								ChangnengUtil.checkFileExt(prefix);
								FastDFSClient fd = new FastDFSClient("classpath:fdfs_client.conf");
								String url = fd.uploadFile(mf.getBytes(), prefix);
								sf.setFileName(myFileName);
								if (prefix.equalsIgnoreCase("pdf")) {
									sf.setFileType("0");
								} else {
									sf.setFileType("1");
								}
								sf.setState("2");
								sf.setFileSize(String.valueOf(mf.getSize()));
								sf.setFileUrl(url);
								sf.setCreateDate(new Date());
								sf.setCreatUserId(sysUsers.getId());
								sf.setCreatUserName(sysUsers.getUsername());
								sysFilesMapper.insertSelective(sf);
								list.add(sf);
							}
						}
					}
				}
			}
		} catch (Exception e) {
			throw new BusinessException(e.getMessage());
		}
		return list;
	}

	@Override
	public void updateAttachmentFileName(String localCheakId, String attachmentFileName) throws BusinessException {
		LocalCheck localCheck = localCheckMapper.selectByPrimaryKey(localCheakId);
		if (localCheck != null) {
			localCheck.setAttachmentFileName(attachmentFileName);
			localCheck.setAdministrativeNoticeAttachment("");
			localCheckMapper.updateByPrimaryKeySelective(localCheck);
		} else {
			throw new BusinessException("现场检查记录不存在");
		}
	}

	@Override
	public List<SysFiles> getAttachmentsByLocalCheckId(String localCheckId) {
		return sysFilesMapper.getAttachmentsByLocalCheckId(localCheckId);

	}

	@Override
	public LocalCheck getLocalCheckById(String localCheckId) {
		return localCheckMapper.selectByPrimaryKey(localCheckId);
	}

	//--↓↓↓↓↓↓↓↓↓↓---
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveEnvSupervisionItems(String localCheckId, String envSupervisionData) throws Exception {

		try {
			// 1. 解析JSON数据
			List<EnvSupervisionItemDTO> items = parseEnvSupervisionData(envSupervisionData);

			if (items == null || items.isEmpty()) {
				logger.warn("环境监管一件事数据为空，localCheckId: {}", localCheckId);
				return;
			}

			// 2. 删除原有的环境监管一件事数据
			localCheckItemMapper.deleteByLocalCheckIdAndFormType(localCheckId, 1);

			// 3. 转换DTO为数据库实体并设置localCheckId
			List<EnvSupervisionItemDTO> dbItems = new ArrayList<>();
			for (EnvSupervisionItemDTO item : items) {
				EnvSupervisionItemDTO dbItem = new EnvSupervisionItemDTO();
				dbItem.setLocalCheckId(localCheckId);
				dbItem.setConfigItemId(item.getConfigItemId());
				dbItem.setItemName(item.getItemName());
				dbItem.setResult(item.getResult());
				dbItem.setProblemDesc(item.getProblemDesc());
				dbItems.add(dbItem);
			}

			// 4. 批量插入新数据
			if (!dbItems.isEmpty()) {
				localCheckItemMapper.batchInsertEnvSupervisionItems(dbItems);
			}

			logger.info("成功保存环境监管一件事数据，localCheckId: {}, 数据条数: {}", localCheckId, dbItems.size());

		} catch (Exception e) {
			logger.error("保存环境监管一件事数据失败，localCheckId: {}", localCheckId, e);
			throw new BusinessException("保存环境监管一件事数据失败: " + e.getMessage());
		}
	}

	@Override
	public List<LocalCheckItem> loadEnvSupervisionItems(String localCheckId) throws Exception {

		try {
			if (localCheckId == null || localCheckId.trim().isEmpty()) {
				return new ArrayList<>();
			}

			List<LocalCheckItem> items = localCheckItemMapper.selectEnvSupervisionItems(localCheckId);

			logger.info("成功加载环境监管一件事数据，localCheckId: {}, 数据条数: {}",
					   localCheckId, items != null ? items.size() : 0);

			return items != null ? items : new ArrayList<>();

		} catch (Exception e) {
			logger.error("加载环境监管一件事数据失败，localCheckId: {}", localCheckId, e);
			throw new BusinessException("加载环境监管一件事数据失败: " + e.getMessage());
		}
	}

	/**
	 * 解析环境监管一件事JSON数据
	 * @param jsonData JSON字符串
	 * @return 解析后的数据列表
	 * @throws Exception 解析异常
	 */
	private List<EnvSupervisionItemDTO> parseEnvSupervisionData(String jsonData) throws Exception {

		try {
			if (jsonData == null || jsonData.trim().isEmpty()) {
				return new ArrayList<>();
			}

			ObjectMapper objectMapper = new ObjectMapper();
			List<EnvSupervisionItemDTO> items = objectMapper.readValue(jsonData,
					new TypeReference<List<EnvSupervisionItemDTO>>() {});

			// 数据验证和清理
			List<EnvSupervisionItemDTO> validItems = new ArrayList<>();
			for (EnvSupervisionItemDTO item : items) {
				if (item.getConfigItemId() != null && !item.getConfigItemId().trim().isEmpty() &&
					item.getResult() != null && !item.getResult().trim().isEmpty()) {

					// 清理数据
					item.setConfigItemId(item.getConfigItemId().trim());
					item.setResult(item.getResult().trim());
					if (item.getItemName() != null) {
						item.setItemName(item.getItemName().trim());
					}
					if (item.getProblemDesc() != null) {
						item.setProblemDesc(item.getProblemDesc().trim());
					}

					validItems.add(item);
				}
			}

			return validItems;

		} catch (Exception e) {
			logger.error("解析环境监管一件事JSON数据失败: {}", jsonData, e);
			throw new BusinessException("解析环境监管一件事数据失败: " + e.getMessage());
		}
	}


	//----------↑↑↑↑↑↑-----

}
